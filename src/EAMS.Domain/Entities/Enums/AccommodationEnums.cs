namespace EAMS.Domain.Entities.Enums;
public enum AccommodationType
{
    Motel,
    Hotel,
    Backpacker,
    Caravan,
    Other
}

public enum Density
{
    Low,
    Medium,
    High
}

public enum Region
{
    Metropolitan,
    Regional,
    Remote
}

public enum Duration
{
    ShortTerm,
    MediumTerm,
    LongTerm,
    Permanent
}

public enum AmenityType
{
    Characteristic,
    Safety,
    Amenity,
    Additional
}